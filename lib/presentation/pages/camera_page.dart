import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:provider/provider.dart';
import 'dart:io';
import '../../core/constants/app_constants.dart';
import '../../core/widgets/custom_button.dart';
import '../../core/widgets/custom_card.dart';
import '../../domain/usecases/analyze_food_image_usecase.dart';
import '../providers/nutrition_provider.dart';
import '../providers/app_provider.dart';

class CameraPage extends StatefulWidget {
  final String mealType;

  const CameraPage({super.key, required this.mealType});

  @override
  State<CameraPage> createState() => _CameraPageState();
}

class _CameraPageState extends State<CameraPage> with TickerProviderStateMixin {
  final ImagePicker _picker = ImagePicker();
  bool _isAnalyzing = false;
  File? _selectedImage;
  String? _analysisResult;
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(begin: 0.8, end: 1.2).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );
    _pulseController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _pulseController.dispose();
    super.dispose();
  }

  String get _mealTypeDisplayName {
    switch (widget.mealType.toLowerCase()) {
      case 'breakfast':
        return 'Breakfast';
      case 'lunch':
        return 'Lunch';
      case 'dinner':
        return 'Dinner';
      case 'snack':
        return 'Snack';
      default:
        return widget.mealType.toUpperCase();
    }
  }

  IconData get _mealTypeIcon {
    switch (widget.mealType.toLowerCase()) {
      case 'breakfast':
        return Icons.free_breakfast;
      case 'lunch':
        return Icons.lunch_dining;
      case 'dinner':
        return Icons.dinner_dining;
      case 'snack':
        return Icons.cookie;
      default:
        return Icons.restaurant;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Add $_mealTypeDisplayName'),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header section
            _buildHeaderSection(),

            const SizedBox(height: AppConstants.defaultPadding),

            // Image preview section
            if (_selectedImage != null) ...[
              _buildImagePreviewSection(),
              const SizedBox(height: AppConstants.defaultPadding),
            ],

            // Analysis result section
            if (_analysisResult != null) ...[
              _buildAnalysisResultSection(),
              const SizedBox(height: AppConstants.defaultPadding),
            ],

            // Action buttons section
            _buildActionButtonsSection(),

            const SizedBox(height: AppConstants.defaultPadding),

            // Tips section
            _buildTipsSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeaderSection() {
    return CustomCard(
      child: Column(
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primaryContainer,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  _mealTypeIcon,
                  size: 32,
                  color: Theme.of(context).colorScheme.onPrimaryContainer,
                ),
              ),
              const SizedBox(width: AppConstants.defaultPadding),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'AI Food Analysis',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Capture or select a photo to analyze your $_mealTypeDisplayName',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildImagePreviewSection() {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Selected Image',
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: AppConstants.defaultPadding),

          ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: Image.file(
              _selectedImage!,
              width: double.infinity,
              height: 200,
              fit: BoxFit.cover,
            ),
          ),

          const SizedBox(height: AppConstants.defaultPadding),

          Row(
            children: [
              Expanded(
                child: CustomButton(
                  text: 'Retake Photo',
                  onPressed: _isAnalyzing ? null : _takePhoto,
                  isOutlined: true,
                  icon: Icons.camera_alt,
                ),
              ),
              const SizedBox(width: AppConstants.smallPadding),
              Expanded(
                child: CustomButton(
                  text: 'Choose Different',
                  onPressed: _isAnalyzing ? null : _pickImageFromGallery,
                  isOutlined: true,
                  icon: Icons.photo_library,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAnalysisResultSection() {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.check_circle, color: Colors.green, size: 20),
              const SizedBox(width: AppConstants.smallPadding),
              Text(
                'Analysis Complete',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: Colors.green,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppConstants.defaultPadding),

          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.green.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
            ),
            child: Text(
              _analysisResult!,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtonsSection() {
    if (_selectedImage == null) {
      return CustomCard(
        child: Column(
          children: [
            if (_isAnalyzing) ...[
              _buildAnalyzingIndicator(),
            ] else ...[
              AnimatedBuilder(
                animation: _pulseAnimation,
                builder: (context, child) {
                  return Transform.scale(
                    scale: _pulseAnimation.value,
                    child: Container(
                      padding: const EdgeInsets.all(24),
                      decoration: BoxDecoration(
                        color: Theme.of(
                          context,
                        ).colorScheme.primaryContainer.withValues(alpha: 0.3),
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        Icons.camera_alt,
                        size: 48,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                    ),
                  );
                },
              ),

              const SizedBox(height: AppConstants.defaultPadding),

              Text(
                'Ready to Analyze Food',
                style: Theme.of(
                  context,
                ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: AppConstants.smallPadding),

              Text(
                'Take a photo or choose from gallery to get started',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: AppConstants.largePadding),

              Row(
                children: [
                  Expanded(
                    child: CustomButton(
                      text: 'Take Photo',
                      onPressed: _takePhoto,
                      icon: Icons.camera_alt,
                    ),
                  ),
                  const SizedBox(width: AppConstants.defaultPadding),
                  Expanded(
                    child: CustomButton(
                      text: 'Gallery',
                      onPressed: _pickImageFromGallery,
                      isOutlined: true,
                      icon: Icons.photo_library,
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      );
    } else {
      return CustomCard(
        child: Column(
          children: [
            if (_isAnalyzing) ...[
              _buildAnalyzingIndicator(),
            ] else ...[
              CustomButton(
                text: 'Analyze Food',
                onPressed: () => _analyzeImage(_selectedImage!),
                icon: Icons.psychology,
                width: double.infinity,
              ),
            ],
          ],
        ),
      );
    }
  }

  Widget _buildAnalyzingIndicator() {
    return Column(
      children: [
        SizedBox(
          width: 60,
          height: 60,
          child: CircularProgressIndicator(
            strokeWidth: 4,
            valueColor: AlwaysStoppedAnimation<Color>(
              Theme.of(context).colorScheme.primary,
            ),
          ),
        ),
        const SizedBox(height: AppConstants.defaultPadding),
        Text(
          'Analyzing Food...',
          style: Theme.of(
            context,
          ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: AppConstants.smallPadding),
        Text(
          'Our AI is identifying the food and calculating nutrition',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildTipsSection() {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.lightbulb, color: Colors.amber, size: 20),
              const SizedBox(width: AppConstants.smallPadding),
              Text(
                'Tips for Better Analysis',
                style: Theme.of(
                  context,
                ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
              ),
            ],
          ),
          const SizedBox(height: AppConstants.defaultPadding),

          _buildTipItem('📸', 'Take clear, well-lit photos'),
          _buildTipItem('🍽️', 'Include the entire meal in frame'),
          _buildTipItem('📏', 'Show portion sizes clearly'),
          _buildTipItem('🏷️', 'Include food labels when possible'),
          _buildTipItem('🔍', 'Avoid blurry or dark images'),
        ],
      ),
    );
  }

  Widget _buildTipItem(String emoji, String tip) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        children: [
          Text(emoji, style: const TextStyle(fontSize: 16)),
          const SizedBox(width: AppConstants.smallPadding),
          Expanded(
            child: Text(tip, style: Theme.of(context).textTheme.bodyMedium),
          ),
        ],
      ),
    );
  }

  Future<void> _takePhoto() async {
    try {
      final XFile? photo = await _picker.pickImage(
        source: ImageSource.camera,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 85,
      );

      if (photo != null) {
        setState(() {
          _selectedImage = File(photo.path);
          _analysisResult = null;
        });
      }
    } catch (e) {
      _showError('Failed to take photo: $e');
    }
  }

  Future<void> _pickImageFromGallery() async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 85,
      );

      if (image != null) {
        setState(() {
          _selectedImage = File(image.path);
          _analysisResult = null;
        });
      }
    } catch (e) {
      _showError('Failed to pick image from gallery: $e');
    }
  }

  Future<void> _analyzeImage(File imageFile) async {
    setState(() => _isAnalyzing = true);

    try {
      final appProvider = context.read<AppProvider>();
      final nutritionProvider = context.read<NutritionProvider>();
      final analyzeFoodImageUseCase = context.read<AnalyzeFoodImageUseCase>();

      if (appProvider.currentUser == null) {
        _showError('User not found');
        return;
      }

      final params = AnalyzeFoodImageParams(
        entryId: DateTime.now().millisecondsSinceEpoch.toString(),
        imageFile: imageFile,
        userId: appProvider.currentUser!.id,
        mealType: widget.mealType,
      );

      final result = await analyzeFoodImageUseCase.execute(params);

      if (result.isSuccess && result.successValue != null) {
        final foodEntry = result.successValue!;

        // Set analysis result for display
        setState(() {
          _analysisResult =
              'Successfully identified: ${foodEntry.name}\n'
              'Calories: ${foodEntry.nutritionData.calories.toStringAsFixed(0)} kcal\n'
              'Protein: ${foodEntry.nutritionData.protein.toStringAsFixed(1)}g\n'
              'Confidence: ${((foodEntry.confidence ?? 0.5) * 100).toStringAsFixed(0)}%';
        });

        // Show success dialog with option to save or edit
        _showAnalysisResultDialog(foodEntry, nutritionProvider);
      } else {
        _showError(result.failureValue?.message ?? 'Failed to analyze image');
      }
    } catch (e) {
      _showError('An error occurred: $e');
    } finally {
      if (mounted) {
        setState(() => _isAnalyzing = false);
      }
    }
  }

  void _showAnalysisResultDialog(dynamic foodEntry, dynamic nutritionProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.check_circle, color: Colors.green, size: 24),
            const SizedBox(width: 8),
            const Text('Analysis Complete'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Food: ${foodEntry.name}',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Text(
              'Calories: ${foodEntry.nutritionData.calories.toStringAsFixed(0)} kcal',
            ),
            Text(
              'Protein: ${foodEntry.nutritionData.protein.toStringAsFixed(1)}g',
            ),
            Text('Carbs: ${foodEntry.nutritionData.carbs.toStringAsFixed(1)}g'),
            Text('Fats: ${foodEntry.nutritionData.fats.toStringAsFixed(1)}g'),
            const SizedBox(height: 8),
            Text(
              'Confidence: ${((foodEntry.confidence ?? 0.5) * 100).toStringAsFixed(0)}%',
              style: TextStyle(
                color: (foodEntry.confidence ?? 0.5) > 0.7
                    ? Colors.green
                    : Colors.orange,
                fontWeight: FontWeight.w500,
              ),
            ),
            if ((foodEntry.confidence ?? 0.5) < 0.7) ...[
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.orange.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: const Text(
                  'Low confidence detected. Please review the analysis.',
                  style: TextStyle(fontSize: 12),
                ),
              ),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              setState(() {
                _analysisResult = null;
              });
            },
            child: const Text('Analyze Again'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // TODO: Navigate to edit food entry page
              _showError('Edit functionality coming soon!');
            },
            child: const Text('Edit'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);

              try {
                await nutritionProvider.addFoodEntry(foodEntry);

                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        '${foodEntry.name} added to $_mealTypeDisplayName!',
                      ),
                      backgroundColor: Colors.green,
                      action: SnackBarAction(
                        label: 'View',
                        onPressed: () {
                          Navigator.of(context).pop();
                        },
                      ),
                    ),
                  );
                  Navigator.of(context).pop();
                }
              } catch (e) {
                _showError('Failed to save food entry: $e');
              }
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  void _showError(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Theme.of(context).colorScheme.error,
          action: SnackBarAction(
            label: 'Dismiss',
            onPressed: () {
              ScaffoldMessenger.of(context).hideCurrentSnackBar();
            },
          ),
        ),
      );
    }
  }
}
