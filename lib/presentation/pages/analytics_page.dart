import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../core/constants/app_constants.dart';
import '../../core/widgets/custom_card.dart';
import '../../core/widgets/custom_button.dart';
import '../providers/nutrition_provider.dart';
import '../providers/app_provider.dart';
import '../../domain/entities/nutritional_goals.dart';

class AnalyticsPage extends StatefulWidget {
  const AnalyticsPage({super.key});

  @override
  State<AnalyticsPage> createState() => _AnalyticsPageState();
}

class _AnalyticsPageState extends State<AnalyticsPage> {
  String _selectedPeriod = 'Week';
  final List<String> _periods = ['Week', 'Month', '3 Months'];

  @override
  Widget build(BuildContext context) {
    return Consumer<NutritionProvider>(
      builder: (context, nutritionProvider, child) {
        final goals = nutritionProvider.nutritionalGoals;
        final progress = nutritionProvider.progressPercentages;

        return SingleChildScrollView(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header section
              _buildHeaderSection(context),

              const SizedBox(height: AppConstants.defaultPadding),

              // Period selector
              _buildPeriodSelector(context),

              const SizedBox(height: AppConstants.defaultPadding),

              if (goals != null) ...[
                // Today's overview
                _buildTodaysOverview(context, goals, progress),

                const SizedBox(height: AppConstants.defaultPadding),

                // Nutrition breakdown chart
                _buildNutritionBreakdownChart(context, goals, progress),

                const SizedBox(height: AppConstants.defaultPadding),

                // Weekly progress chart
                _buildWeeklyProgressChart(context),

                const SizedBox(height: AppConstants.defaultPadding),

                // Insights and recommendations
                _buildInsightsSection(context, goals, progress),
              ] else ...[
                _buildNoDataMessage(context),
              ],
            ],
          ),
        );
      },
    );
  }

  Widget _buildHeaderSection(BuildContext context) {
    return CustomCard(
      child: Column(
        children: [
          Icon(
            Icons.analytics,
            size: 48,
            color: Theme.of(context).colorScheme.primary,
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            'Nutrition Analytics',
            style: Theme.of(
              context,
            ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            'Track your nutrition trends and insights',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildPeriodSelector(BuildContext context) {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Time Period',
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Row(
            children: _periods.map((period) {
              final isSelected = period == _selectedPeriod;
              return Expanded(
                child: Padding(
                  padding: const EdgeInsets.only(right: 8.0),
                  child: CustomButton(
                    text: period,
                    isOutlined: !isSelected,
                    onPressed: () {
                      setState(() {
                        _selectedPeriod = period;
                      });
                    },
                  ),
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildTodaysOverview(
    BuildContext context,
    NutritionalGoals goals,
    Map<String, double> progress,
  ) {
    final currentData = context.watch<NutritionProvider>().currentDayData;
    final totalNutrition = currentData?.dailyLog.totalNutrition;

    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Today\'s Summary',
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: AppConstants.defaultPadding),

          if (totalNutrition != null) ...[
            Row(
              children: [
                Expanded(
                  child: _buildSummaryItem(
                    context,
                    'Calories',
                    '${totalNutrition.calories.toStringAsFixed(0)}',
                    '${goals.dailyCalories.toStringAsFixed(0)}',
                    progress['calories'] ?? 0,
                    Icons.local_fire_department,
                    Colors.orange,
                  ),
                ),
                const SizedBox(width: AppConstants.smallPadding),
                Expanded(
                  child: _buildSummaryItem(
                    context,
                    'Protein',
                    '${totalNutrition.protein.toStringAsFixed(1)}g',
                    '${goals.dailyProtein.toStringAsFixed(1)}g',
                    progress['protein'] ?? 0,
                    Icons.fitness_center,
                    Colors.red,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Row(
              children: [
                Expanded(
                  child: _buildSummaryItem(
                    context,
                    'Carbs',
                    '${totalNutrition.carbs.toStringAsFixed(1)}g',
                    '${goals.dailyCarbs.toStringAsFixed(1)}g',
                    progress['carbs'] ?? 0,
                    Icons.grain,
                    Colors.amber,
                  ),
                ),
                const SizedBox(width: AppConstants.smallPadding),
                Expanded(
                  child: _buildSummaryItem(
                    context,
                    'Fats',
                    '${totalNutrition.fats.toStringAsFixed(1)}g',
                    '${goals.dailyFats.toStringAsFixed(1)}g',
                    progress['fats'] ?? 0,
                    Icons.opacity,
                    Colors.blue,
                  ),
                ),
              ],
            ),
          ] else ...[
            const Center(child: Text('No nutrition data for today')),
          ],
        ],
      ),
    );
  }

  Widget _buildSummaryItem(
    BuildContext context,
    String label,
    String current,
    String target,
    double percentage,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 4),
          Text(
            label,
            style: Theme.of(
              context,
            ).textTheme.bodySmall?.copyWith(fontWeight: FontWeight.w500),
          ),
          const SizedBox(height: 2),
          Text(
            current,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            'of $target',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            '${percentage.toStringAsFixed(0)}%',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              fontWeight: FontWeight.w600,
              color: percentage > 100 ? Colors.red : color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNutritionBreakdownChart(
    BuildContext context,
    NutritionalGoals goals,
    Map<String, double> progress,
  ) {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Macronutrient Breakdown',
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: AppConstants.defaultPadding),

          SizedBox(
            height: 200,
            child: PieChart(
              PieChartData(
                sections: _getPieChartSections(progress),
                centerSpaceRadius: 40,
                sectionsSpace: 2,
              ),
            ),
          ),

          const SizedBox(height: AppConstants.defaultPadding),

          // Legend
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildLegendItem(context, 'Protein', Colors.red),
              _buildLegendItem(context, 'Carbs', Colors.amber),
              _buildLegendItem(context, 'Fats', Colors.blue),
            ],
          ),
        ],
      ),
    );
  }

  List<PieChartSectionData> _getPieChartSections(Map<String, double> progress) {
    final protein = progress['protein'] ?? 0;
    final carbs = progress['carbs'] ?? 0;
    final fats = progress['fats'] ?? 0;

    final total = protein + carbs + fats;
    if (total == 0) {
      return [
        PieChartSectionData(
          color: Colors.grey,
          value: 100,
          title: 'No Data',
          radius: 50,
        ),
      ];
    }

    return [
      PieChartSectionData(
        color: Colors.red,
        value: protein,
        title: '${(protein / total * 100).toStringAsFixed(0)}%',
        radius: 50,
        titleStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
      PieChartSectionData(
        color: Colors.amber,
        value: carbs,
        title: '${(carbs / total * 100).toStringAsFixed(0)}%',
        radius: 50,
        titleStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
      PieChartSectionData(
        color: Colors.blue,
        value: fats,
        title: '${(fats / total * 100).toStringAsFixed(0)}%',
        radius: 50,
        titleStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
    ];
  }

  Widget _buildLegendItem(BuildContext context, String label, Color color) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(color: color, shape: BoxShape.circle),
        ),
        const SizedBox(width: 4),
        Text(label, style: Theme.of(context).textTheme.bodySmall),
      ],
    );
  }

  Widget _buildWeeklyProgressChart(BuildContext context) {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Weekly Progress',
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: AppConstants.defaultPadding),

          SizedBox(
            height: 200,
            child: LineChart(
              LineChartData(
                gridData: const FlGridData(show: true),
                titlesData: FlTitlesData(
                  leftTitles: const AxisTitles(
                    sideTitles: SideTitles(showTitles: true, reservedSize: 40),
                  ),
                  bottomTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      getTitlesWidget: (value, meta) {
                        const days = [
                          'Mon',
                          'Tue',
                          'Wed',
                          'Thu',
                          'Fri',
                          'Sat',
                          'Sun',
                        ];
                        if (value.toInt() >= 0 && value.toInt() < days.length) {
                          return Text(
                            days[value.toInt()],
                            style: Theme.of(context).textTheme.bodySmall,
                          );
                        }
                        return const Text('');
                      },
                    ),
                  ),
                  topTitles: const AxisTitles(
                    sideTitles: SideTitles(showTitles: false),
                  ),
                  rightTitles: const AxisTitles(
                    sideTitles: SideTitles(showTitles: false),
                  ),
                ),
                borderData: FlBorderData(show: true),
                lineBarsData: [
                  LineChartBarData(
                    spots: _getWeeklyCalorieSpots(),
                    isCurved: true,
                    color: Theme.of(context).colorScheme.primary,
                    barWidth: 3,
                    dotData: const FlDotData(show: true),
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: AppConstants.smallPadding),

          Text(
            'Daily calorie intake over the past week',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  List<FlSpot> _getWeeklyCalorieSpots() {
    // Mock data for demonstration
    return [
      const FlSpot(0, 1800),
      const FlSpot(1, 2100),
      const FlSpot(2, 1950),
      const FlSpot(3, 2200),
      const FlSpot(4, 1750),
      const FlSpot(5, 2300),
      const FlSpot(6, 2000),
    ];
  }

  Widget _buildInsightsSection(
    BuildContext context,
    NutritionalGoals goals,
    Map<String, double> progress,
  ) {
    final insights = _generateInsights(goals, progress);

    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Insights & Recommendations',
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: AppConstants.defaultPadding),

          ...insights.map(
            (insight) => Padding(
              padding: const EdgeInsets.only(bottom: 12.0),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Icon(insight.icon, color: insight.color, size: 20),
                  const SizedBox(width: AppConstants.smallPadding),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          insight.title,
                          style: Theme.of(context).textTheme.bodyMedium
                              ?.copyWith(fontWeight: FontWeight.w600),
                        ),
                        const SizedBox(height: 2),
                        Text(
                          insight.description,
                          style: Theme.of(context).textTheme.bodySmall
                              ?.copyWith(
                                color: Theme.of(
                                  context,
                                ).colorScheme.onSurfaceVariant,
                              ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<NutritionInsight> _generateInsights(
    NutritionalGoals goals,
    Map<String, double> progress,
  ) {
    final insights = <NutritionInsight>[];

    final calorieProgress = progress['calories'] ?? 0;
    final proteinProgress = progress['protein'] ?? 0;
    final carbProgress = progress['carbs'] ?? 0;
    final fatProgress = progress['fats'] ?? 0;

    if (calorieProgress < 80) {
      insights.add(
        NutritionInsight(
          icon: Icons.warning,
          color: Colors.orange,
          title: 'Low Calorie Intake',
          description:
              'You\'re consuming fewer calories than your goal. Consider adding healthy snacks.',
        ),
      );
    } else if (calorieProgress > 120) {
      insights.add(
        NutritionInsight(
          icon: Icons.info,
          color: Colors.blue,
          title: 'High Calorie Intake',
          description:
              'You\'ve exceeded your calorie goal. Try to balance with more activity.',
        ),
      );
    }

    if (proteinProgress < 70) {
      insights.add(
        NutritionInsight(
          icon: Icons.fitness_center,
          color: Colors.red,
          title: 'Increase Protein',
          description:
              'Add more protein-rich foods like lean meats, eggs, or legumes.',
        ),
      );
    }

    if (insights.isEmpty) {
      insights.add(
        NutritionInsight(
          icon: Icons.check_circle,
          color: Colors.green,
          title: 'Great Progress!',
          description:
              'You\'re meeting your nutrition goals well. Keep up the good work!',
        ),
      );
    }

    return insights;
  }

  Widget _buildNoDataMessage(BuildContext context) {
    return CustomCard(
      child: Column(
        children: [
          Icon(
            Icons.analytics_outlined,
            size: 64,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            'No Analytics Available',
            style: Theme.of(
              context,
            ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            'Start tracking your nutrition to see detailed analytics and insights.',
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          CustomButton(
            text: 'Add Food Entry',
            icon: Icons.add,
            onPressed: () {
              // Navigate to camera or add food page
              Navigator.pushNamed(context, '/camera');
            },
          ),
        ],
      ),
    );
  }
}

class NutritionInsight {
  final IconData icon;
  final Color color;
  final String title;
  final String description;

  NutritionInsight({
    required this.icon,
    required this.color,
    required this.title,
    required this.description,
  });
}
