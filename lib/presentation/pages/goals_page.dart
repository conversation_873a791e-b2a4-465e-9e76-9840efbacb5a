import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../../core/constants/app_constants.dart';
import '../../core/widgets/custom_card.dart';
import '../../core/widgets/custom_button.dart';
import '../providers/nutrition_provider.dart';
import '../providers/app_provider.dart';
import '../../domain/entities/nutritional_goals.dart';

class GoalsPage extends StatefulWidget {
  const GoalsPage({super.key});

  @override
  State<GoalsPage> createState() => _GoalsPageState();
}

class _GoalsPageState extends State<GoalsPage> {
  bool _isEditing = false;
  final _formKey = GlobalKey<FormState>();

  // Controllers for form fields
  late TextEditingController _caloriesController;
  late TextEditingController _proteinController;
  late TextEditingController _carbsController;
  late TextEditingController _fatsController;
  late TextEditingController _fiberController;
  late TextEditingController _sugarController;
  late TextEditingController _sodiumController;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
  }

  void _initializeControllers() {
    _caloriesController = TextEditingController();
    _proteinController = TextEditingController();
    _carbsController = TextEditingController();
    _fatsController = TextEditingController();
    _fiberController = TextEditingController();
    _sugarController = TextEditingController();
    _sodiumController = TextEditingController();
  }

  @override
  void dispose() {
    _caloriesController.dispose();
    _proteinController.dispose();
    _carbsController.dispose();
    _fatsController.dispose();
    _fiberController.dispose();
    _sugarController.dispose();
    _sodiumController.dispose();
    super.dispose();
  }

  void _populateControllers(NutritionalGoals goals) {
    _caloriesController.text = goals.dailyCalories.toStringAsFixed(0);
    _proteinController.text = goals.dailyProtein.toStringAsFixed(1);
    _carbsController.text = goals.dailyCarbs.toStringAsFixed(1);
    _fatsController.text = goals.dailyFats.toStringAsFixed(1);
    _fiberController.text = goals.dailyFiber.toStringAsFixed(1);
    _sugarController.text = goals.dailySugar.toStringAsFixed(1);
    _sodiumController.text = goals.dailySodium.toStringAsFixed(0);
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<NutritionProvider>(
      builder: (context, nutritionProvider, child) {
        final goals = nutritionProvider.nutritionalGoals;

        if (goals != null && !_isEditing) {
          _populateControllers(goals);
        }

        return SingleChildScrollView(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header section
              _buildHeaderSection(context, goals),

              const SizedBox(height: AppConstants.defaultPadding),

              if (goals != null) ...[
                // Current goals overview
                _buildGoalsOverview(context, goals),

                const SizedBox(height: AppConstants.defaultPadding),

                // Detailed goals form
                _buildGoalsForm(context, goals),
              ] else ...[
                _buildNoGoalsMessage(context),
              ],
            ],
          ),
        );
      },
    );
  }

  Widget _buildHeaderSection(BuildContext context, NutritionalGoals? goals) {
    return CustomCard(
      child: Column(
        children: [
          Icon(
            Icons.track_changes,
            size: 48,
            color: Theme.of(context).colorScheme.primary,
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            'Nutrition Goals',
            style: Theme.of(
              context,
            ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            'Track and customize your daily nutrition targets',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
          if (goals != null) ...[
            const SizedBox(height: AppConstants.defaultPadding),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                Expanded(
                  child: CustomButton(
                    text: _isEditing ? 'Cancel' : 'Edit Goals',
                    icon: _isEditing ? Icons.close : Icons.edit,
                    isOutlined: _isEditing,
                    onPressed: () {
                      setState(() {
                        _isEditing = !_isEditing;
                        if (!_isEditing) {
                          _populateControllers(goals);
                        }
                      });
                    },
                  ),
                ),
                if (_isEditing) ...[
                  const SizedBox(width: AppConstants.defaultPadding),
                  Expanded(
                    child: CustomButton(
                      text: 'Save',
                      icon: Icons.save,
                      onPressed: _saveGoals,
                    ),
                  ),
                ],
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildGoalsOverview(BuildContext context, NutritionalGoals goals) {
    final progress = context.watch<NutritionProvider>().progressPercentages;

    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Today\'s Progress',
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          _buildProgressItem(
            context,
            'Calories',
            progress['calories'] ?? 0,
            '${goals.dailyCalories.toStringAsFixed(0)} kcal',
            Icons.local_fire_department,
            Colors.orange,
          ),
          const SizedBox(height: AppConstants.smallPadding),
          _buildProgressItem(
            context,
            'Protein',
            progress['protein'] ?? 0,
            '${goals.dailyProtein.toStringAsFixed(1)} g',
            Icons.fitness_center,
            Colors.red,
          ),
          const SizedBox(height: AppConstants.smallPadding),
          _buildProgressItem(
            context,
            'Carbs',
            progress['carbs'] ?? 0,
            '${goals.dailyCarbs.toStringAsFixed(1)} g',
            Icons.grain,
            Colors.amber,
          ),
          const SizedBox(height: AppConstants.smallPadding),
          _buildProgressItem(
            context,
            'Fats',
            progress['fats'] ?? 0,
            '${goals.dailyFats.toStringAsFixed(1)} g',
            Icons.opacity,
            Colors.blue,
          ),
        ],
      ),
    );
  }

  Widget _buildProgressItem(
    BuildContext context,
    String label,
    double percentage,
    String target,
    IconData icon,
    Color color,
  ) {
    return Row(
      children: [
        Icon(icon, color: color, size: 20),
        const SizedBox(width: AppConstants.smallPadding),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    label,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Text(
                    target,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              LinearProgressIndicator(
                value: (percentage / 100).clamp(0.0, 1.0),
                backgroundColor: color.withValues(alpha: 0.2),
                valueColor: AlwaysStoppedAnimation<Color>(color),
              ),
              const SizedBox(height: 2),
              Text(
                '${percentage.toStringAsFixed(1)}%',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildGoalsForm(BuildContext context, NutritionalGoals goals) {
    if (!_isEditing) {
      return _buildGoalsDisplay(context, goals);
    }

    return Form(
      key: _formKey,
      child: CustomCard(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Edit Nutrition Goals',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: AppConstants.defaultPadding),

            // Macronutrients section
            _buildSectionHeader(context, 'Macronutrients'),
            const SizedBox(height: AppConstants.smallPadding),

            _buildNumberField(
              controller: _caloriesController,
              label: 'Daily Calories',
              suffix: 'kcal',
              validator: (value) =>
                  _validateNumber(value, 'Calories', 800, 5000),
            ),
            const SizedBox(height: AppConstants.smallPadding),

            Row(
              children: [
                Expanded(
                  child: _buildNumberField(
                    controller: _proteinController,
                    label: 'Protein',
                    suffix: 'g',
                    validator: (value) =>
                        _validateNumber(value, 'Protein', 10, 300),
                  ),
                ),
                const SizedBox(width: AppConstants.smallPadding),
                Expanded(
                  child: _buildNumberField(
                    controller: _carbsController,
                    label: 'Carbs',
                    suffix: 'g',
                    validator: (value) =>
                        _validateNumber(value, 'Carbs', 20, 800),
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.smallPadding),

            Row(
              children: [
                Expanded(
                  child: _buildNumberField(
                    controller: _fatsController,
                    label: 'Fats',
                    suffix: 'g',
                    validator: (value) =>
                        _validateNumber(value, 'Fats', 10, 200),
                  ),
                ),
                const SizedBox(width: AppConstants.smallPadding),
                Expanded(
                  child: _buildNumberField(
                    controller: _fiberController,
                    label: 'Fiber',
                    suffix: 'g',
                    validator: (value) =>
                        _validateNumber(value, 'Fiber', 5, 100),
                  ),
                ),
              ],
            ),

            const SizedBox(height: AppConstants.defaultPadding),

            // Limits section
            _buildSectionHeader(context, 'Daily Limits'),
            const SizedBox(height: AppConstants.smallPadding),

            Row(
              children: [
                Expanded(
                  child: _buildNumberField(
                    controller: _sugarController,
                    label: 'Sugar Limit',
                    suffix: 'g',
                    validator: (value) =>
                        _validateNumber(value, 'Sugar', 0, 200),
                  ),
                ),
                const SizedBox(width: AppConstants.smallPadding),
                Expanded(
                  child: _buildNumberField(
                    controller: _sodiumController,
                    label: 'Sodium Limit',
                    suffix: 'mg',
                    validator: (value) =>
                        _validateNumber(value, 'Sodium', 500, 5000),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGoalsDisplay(BuildContext context, NutritionalGoals goals) {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Current Goals',
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: AppConstants.defaultPadding),

          _buildGoalDisplayItem(
            context,
            'Daily Calories',
            '${goals.dailyCalories.toStringAsFixed(0)} kcal',
            Icons.local_fire_department,
          ),
          _buildGoalDisplayItem(
            context,
            'Protein',
            '${goals.dailyProtein.toStringAsFixed(1)} g',
            Icons.fitness_center,
          ),
          _buildGoalDisplayItem(
            context,
            'Carbohydrates',
            '${goals.dailyCarbs.toStringAsFixed(1)} g',
            Icons.grain,
          ),
          _buildGoalDisplayItem(
            context,
            'Fats',
            '${goals.dailyFats.toStringAsFixed(1)} g',
            Icons.opacity,
          ),
          _buildGoalDisplayItem(
            context,
            'Fiber',
            '${goals.dailyFiber.toStringAsFixed(1)} g',
            Icons.eco,
          ),
          _buildGoalDisplayItem(
            context,
            'Sugar Limit',
            '${goals.dailySugar.toStringAsFixed(1)} g',
            Icons.warning,
          ),
          _buildGoalDisplayItem(
            context,
            'Sodium Limit',
            '${goals.dailySodium.toStringAsFixed(0)} mg',
            Icons.water_drop,
          ),
        ],
      ),
    );
  }

  Widget _buildGoalDisplayItem(
    BuildContext context,
    String label,
    String value,
    IconData icon,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        children: [
          Icon(icon, size: 20, color: Theme.of(context).colorScheme.primary),
          const SizedBox(width: AppConstants.smallPadding),
          Expanded(
            child: Text(label, style: Theme.of(context).textTheme.bodyMedium),
          ),
          Text(
            value,
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w600),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(BuildContext context, String title) {
    return Text(
      title,
      style: Theme.of(context).textTheme.titleSmall?.copyWith(
        fontWeight: FontWeight.w600,
        color: Theme.of(context).colorScheme.primary,
      ),
    );
  }

  Widget _buildNumberField({
    required TextEditingController controller,
    required String label,
    required String suffix,
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      keyboardType: const TextInputType.numberWithOptions(decimal: true),
      inputFormatters: [
        FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
      ],
      decoration: InputDecoration(
        labelText: label,
        suffixText: suffix,
        border: const OutlineInputBorder(),
      ),
      validator: validator,
    );
  }

  String? _validateNumber(
    String? value,
    String fieldName,
    double min,
    double max,
  ) {
    if (value == null || value.isEmpty) {
      return '$fieldName is required';
    }

    final number = double.tryParse(value);
    if (number == null) {
      return 'Please enter a valid number';
    }

    if (number < min || number > max) {
      return '$fieldName must be between $min and $max';
    }

    return null;
  }

  Widget _buildNoGoalsMessage(BuildContext context) {
    return CustomCard(
      child: Column(
        children: [
          Icon(
            Icons.track_changes_outlined,
            size: 64,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            'No Goals Set',
            style: Theme.of(
              context,
            ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            'Complete your profile setup to get personalized nutrition goals.',
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          CustomButton(
            text: 'Set Up Profile',
            icon: Icons.person_add,
            onPressed: () {
              Navigator.pushNamed(context, '/profile');
            },
          ),
        ],
      ),
    );
  }

  void _saveGoals() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    final user = context.read<AppProvider>().currentUser;
    if (user == null) return;

    final currentGoals = context.read<NutritionProvider>().nutritionalGoals;
    if (currentGoals == null) return;

    try {
      final updatedGoals = currentGoals.copyWith(
        dailyCalories: double.parse(_caloriesController.text),
        dailyProtein: double.parse(_proteinController.text),
        dailyCarbs: double.parse(_carbsController.text),
        dailyFats: double.parse(_fatsController.text),
        dailyFiber: double.parse(_fiberController.text),
        dailySugar: double.parse(_sugarController.text),
        dailySodium: double.parse(_sodiumController.text),
        updatedAt: DateTime.now(),
      );

      // TODO: Add update goals functionality to nutrition provider
      // await context.read<NutritionProvider>().updateGoals(updatedGoals);

      setState(() {
        _isEditing = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Goals updated successfully!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update goals: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
