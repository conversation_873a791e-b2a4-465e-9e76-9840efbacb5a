import 'package:flutter/material.dart';
import 'dart:io';
import '../../core/constants/app_constants.dart';
import '../../core/widgets/custom_button.dart';
import '../../core/widgets/custom_card.dart';

class FoodAnalysisPage extends StatefulWidget {
  final String imagePath;
  final String mealType;
  final Map<String, dynamic>? analysisResult;

  const FoodAnalysisPage({
    super.key,
    required this.imagePath,
    required this.mealType,
    this.analysisResult,
  });

  @override
  State<FoodAnalysisPage> createState() => _FoodAnalysisPageState();
}

class _FoodAnalysisPageState extends State<FoodAnalysisPage>
    with TickerProviderStateMixin {
  bool _isAnalyzing = false;
  Map<String, dynamic>? _currentAnalysis;
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _currentAnalysis = widget.analysisResult;

    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    _fadeController.forward();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    super.dispose();
  }

  String get _mealTypeDisplayName {
    switch (widget.mealType.toLowerCase()) {
      case 'breakfast':
        return 'Breakfast';
      case 'lunch':
        return 'Lunch';
      case 'dinner':
        return 'Dinner';
      case 'snack':
        return 'Snack';
      default:
        return widget.mealType.toUpperCase();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('$_mealTypeDisplayName Analysis'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.help_outline),
            onPressed: _showHelpDialog,
          ),
        ],
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Image preview section
              _buildImagePreviewSection(),

              const SizedBox(height: AppConstants.defaultPadding),

              // Analysis results section
              if (_isAnalyzing) ...[
                _buildAnalyzingSection(),
              ] else if (_currentAnalysis != null) ...[
                _buildAnalysisResultsSection(),
              ] else ...[
                _buildNoAnalysisSection(),
              ],

              const SizedBox(height: AppConstants.defaultPadding),

              // Action buttons
              _buildActionButtonsSection(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildImagePreviewSection() {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Food Image',
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: AppConstants.defaultPadding),

          ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: Image.file(
              File(widget.imagePath),
              width: double.infinity,
              height: 200,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  width: double.infinity,
                  height: 200,
                  decoration: BoxDecoration(
                    color: Theme.of(
                      context,
                    ).colorScheme.surfaceContainerHighest,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.broken_image,
                        size: 48,
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Image not found',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAnalyzingSection() {
    return CustomCard(
      child: Column(
        children: [
          SizedBox(
            width: 80,
            height: 80,
            child: CircularProgressIndicator(
              strokeWidth: 6,
              valueColor: AlwaysStoppedAnimation<Color>(
                Theme.of(context).colorScheme.primary,
              ),
            ),
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            'Analyzing Food...',
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            'Our AI is identifying the food and calculating nutritional information',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          LinearProgressIndicator(
            backgroundColor: Theme.of(
              context,
            ).colorScheme.surfaceContainerHighest,
            valueColor: AlwaysStoppedAnimation<Color>(
              Theme.of(context).colorScheme.primary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAnalysisResultsSection() {
    final analysis = _currentAnalysis!;
    final nutrition = analysis['nutrition'] as Map<String, dynamic>? ?? {};
    final confidence = (analysis['confidence'] as num?)?.toDouble() ?? 0.5;

    return Column(
      children: [
        // Food identification card
        CustomCard(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.check_circle,
                    color: confidence > 0.7 ? Colors.green : Colors.orange,
                    size: 24,
                  ),
                  const SizedBox(width: AppConstants.smallPadding),
                  Text(
                    'Food Identified',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: confidence > 0.7 ? Colors.green : Colors.orange,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: AppConstants.defaultPadding),

              Text(
                analysis['food_name'] as String? ?? 'Unknown Food',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),

              if (analysis['description'] != null) ...[
                const SizedBox(height: AppConstants.smallPadding),
                Text(
                  analysis['description'] as String,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ],

              const SizedBox(height: AppConstants.defaultPadding),

              Row(
                children: [
                  Expanded(
                    child: _buildInfoChip(
                      'Serving',
                      '${analysis['serving_size'] ?? 1} ${analysis['serving_unit'] ?? 'serving'}',
                      Icons.straighten,
                    ),
                  ),
                  const SizedBox(width: AppConstants.smallPadding),
                  Expanded(
                    child: _buildInfoChip(
                      'Confidence',
                      '${(confidence * 100).toStringAsFixed(0)}%',
                      Icons.psychology,
                      color: confidence > 0.7 ? Colors.green : Colors.orange,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),

        const SizedBox(height: AppConstants.defaultPadding),

        // Nutrition information card
        _buildNutritionCard(nutrition),
      ],
    );
  }

  Widget _buildInfoChip(
    String label,
    String value,
    IconData icon, {
    Color? color,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: (color ?? Theme.of(context).colorScheme.primary).withValues(
          alpha: 0.1,
        ),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: (color ?? Theme.of(context).colorScheme.primary).withValues(
            alpha: 0.3,
          ),
        ),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: color ?? Theme.of(context).colorScheme.primary,
            size: 20,
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: Theme.of(
              context,
            ).textTheme.bodySmall?.copyWith(fontWeight: FontWeight.w500),
          ),
          const SizedBox(height: 2),
          Text(
            value,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: color ?? Theme.of(context).colorScheme.primary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNutritionCard(Map<String, dynamic> nutrition) {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Nutritional Information',
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: AppConstants.defaultPadding),

          // Main macros
          Row(
            children: [
              Expanded(
                child: _buildNutritionItem(
                  'Calories',
                  '${(nutrition['calories'] as num?)?.toStringAsFixed(0) ?? '0'} kcal',
                  Icons.local_fire_department,
                  Colors.orange,
                ),
              ),
              const SizedBox(width: AppConstants.smallPadding),
              Expanded(
                child: _buildNutritionItem(
                  'Protein',
                  '${(nutrition['protein'] as num?)?.toStringAsFixed(1) ?? '0'}g',
                  Icons.fitness_center,
                  Colors.red,
                ),
              ),
            ],
          ),

          const SizedBox(height: AppConstants.smallPadding),

          Row(
            children: [
              Expanded(
                child: _buildNutritionItem(
                  'Carbs',
                  '${(nutrition['carbs'] as num?)?.toStringAsFixed(1) ?? '0'}g',
                  Icons.grain,
                  Colors.amber,
                ),
              ),
              const SizedBox(width: AppConstants.smallPadding),
              Expanded(
                child: _buildNutritionItem(
                  'Fats',
                  '${(nutrition['fats'] as num?)?.toStringAsFixed(1) ?? '0'}g',
                  Icons.opacity,
                  Colors.blue,
                ),
              ),
            ],
          ),

          const SizedBox(height: AppConstants.defaultPadding),

          // Additional nutrients
          ExpansionTile(
            title: const Text('More Nutrients'),
            children: [
              _buildDetailedNutritionRow('Fiber', nutrition['fiber'], 'g'),
              _buildDetailedNutritionRow('Sugar', nutrition['sugar'], 'g'),
              _buildDetailedNutritionRow('Sodium', nutrition['sodium'], 'mg'),
              _buildDetailedNutritionRow(
                'Saturated Fat',
                nutrition['saturatedFat'],
                'g',
              ),
              _buildDetailedNutritionRow(
                'Cholesterol',
                nutrition['cholesterol'],
                'mg',
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildNutritionItem(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 4),
          Text(
            label,
            style: Theme.of(
              context,
            ).textTheme.bodySmall?.copyWith(fontWeight: FontWeight.w500),
          ),
          const SizedBox(height: 2),
          Text(
            value,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailedNutritionRow(String label, dynamic value, String unit) {
    final displayValue = value != null
        ? '${(value as num).toStringAsFixed(1)}$unit'
        : 'N/A';

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(
            displayValue,
            style: const TextStyle(fontWeight: FontWeight.w500),
          ),
        ],
      ),
    );
  }

  Widget _buildNoAnalysisSection() {
    return CustomCard(
      child: Column(
        children: [
          Icon(
            Icons.psychology_outlined,
            size: 64,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            'No Analysis Available',
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            'Start the analysis to identify the food and get nutritional information.',
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtonsSection() {
    return Column(
      children: [
        if (_currentAnalysis != null && !_isAnalyzing) ...[
          CustomButton(
            text: 'Save to $_mealTypeDisplayName',
            onPressed: _saveFoodEntry,
            icon: Icons.save,
            width: double.infinity,
          ),
          const SizedBox(height: AppConstants.smallPadding),
        ],

        Row(
          children: [
            Expanded(
              child: CustomButton(
                text: _isAnalyzing ? 'Analyzing...' : 'Analyze Again',
                onPressed: _isAnalyzing ? null : _startAnalysis,
                isOutlined: true,
                icon: Icons.refresh,
                isLoading: _isAnalyzing,
              ),
            ),

            if (_currentAnalysis != null) ...[
              const SizedBox(width: AppConstants.defaultPadding),
              Expanded(
                child: CustomButton(
                  text: 'Edit Details',
                  onPressed: _editFoodDetails,
                  isOutlined: true,
                  icon: Icons.edit,
                ),
              ),
            ],
          ],
        ),
      ],
    );
  }

  void _startAnalysis() {
    setState(() {
      _isAnalyzing = true;
      _currentAnalysis = null;
    });

    // Simulate analysis - in real implementation, this would call the AI service
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted) {
        setState(() {
          _isAnalyzing = false;
          _currentAnalysis = {
            'food_name': 'Sample Food Item',
            'description': 'A delicious and nutritious food item',
            'serving_size': 100,
            'serving_unit': 'grams',
            'confidence': 0.85,
            'nutrition': {
              'calories': 250.0,
              'protein': 15.0,
              'carbs': 30.0,
              'fats': 8.0,
              'fiber': 5.0,
              'sugar': 12.0,
              'sodium': 300.0,
              'saturatedFat': 2.0,
              'cholesterol': 25.0,
            },
          };
        });
      }
    });
  }

  void _saveFoodEntry() {
    // TODO: Implement saving food entry
    Navigator.pop(context);
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Food entry saved to $_mealTypeDisplayName!'),
        backgroundColor: Colors.green,
        action: SnackBarAction(
          label: 'View',
          onPressed: () {
            // Navigate back to home or meal view
          },
        ),
      ),
    );
  }

  void _editFoodDetails() {
    // TODO: Navigate to edit food details page
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Edit functionality coming soon!')),
    );
  }

  void _showHelpDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Food Analysis Help'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'How Food Analysis Works:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text('• AI analyzes your food image'),
            Text('• Identifies food items and ingredients'),
            Text('• Calculates nutritional information'),
            Text('• Provides confidence score'),
            SizedBox(height: 16),
            Text(
              'Tips for Better Results:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text('• Use clear, well-lit photos'),
            Text('• Include the entire food item'),
            Text('• Avoid blurry or dark images'),
            Text('• Show food labels when possible'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Got it'),
          ),
        ],
      ),
    );
  }
}
