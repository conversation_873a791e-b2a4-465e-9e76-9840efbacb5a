import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../core/constants/app_constants.dart';
import '../../core/routes/app_routes.dart';
import '../../core/widgets/custom_card.dart';
import '../../core/widgets/custom_button.dart';
import '../../core/utils/date_utils.dart';
import '../providers/app_provider.dart';
import '../providers/nutrition_provider.dart';
import '../widgets/nutrition_progress_card.dart';
import '../widgets/meal_section.dart';
import '../widgets/quick_stats_card.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadData();
    });
  }

  Future<void> _loadData() async {
    final appProvider = context.read<AppProvider>();
    final nutritionProvider = context.read<NutritionProvider>();

    if (appProvider.currentUser != null) {
      await nutritionProvider.loadDailyNutrition(appProvider.currentUser!.id);
      await nutritionProvider.loadRecentEntries(appProvider.currentUser!.id);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Consumer<NutritionProvider>(
        builder: (context, nutritionProvider, child) {
          if (nutritionProvider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (nutritionProvider.error != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Theme.of(context).colorScheme.error,
                  ),
                  const SizedBox(height: AppConstants.defaultPadding),
                  Text(
                    'Error loading data',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  const SizedBox(height: AppConstants.smallPadding),
                  Text(
                    nutritionProvider.error!,
                    textAlign: TextAlign.center,
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                  const SizedBox(height: AppConstants.defaultPadding),
                  CustomButton(
                    text: 'Retry',
                    onPressed: _loadData,
                    icon: Icons.refresh,
                  ),
                ],
              ),
            );
          }

          return RefreshIndicator(
            onRefresh: _loadData,
            child: SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              child: Padding(
                padding: const EdgeInsets.all(AppConstants.defaultPadding),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Calendar view slider
                    _buildCalendarSlider(nutritionProvider),

                    const SizedBox(height: AppConstants.defaultPadding),

                    // Quick stats
                    const QuickStatsCard(),

                    const SizedBox(height: AppConstants.defaultPadding),

                    // Nutrition progress
                    const NutritionProgressCard(),

                    const SizedBox(height: AppConstants.defaultPadding),

                    // Quick add food button
                    _buildQuickAddSection(),

                    const SizedBox(height: AppConstants.defaultPadding),

                    // Meals section
                    _buildMealsSection(nutritionProvider),

                    const SizedBox(height: AppConstants.defaultPadding),

                    // Recent entries
                    _buildRecentEntries(nutritionProvider),
                  ],
                ),
              ),
            ),
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showAddFoodOptions(context),
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildCalendarSlider(NutritionProvider nutritionProvider) {
    final theme = Theme.of(context);
    final selectedDate = nutritionProvider.selectedDate;
    final now = DateTime.now();

    // Generate dates for the past 7 days and today
    final dates = List.generate(8, (index) {
      return now.subtract(Duration(days: 7 - index));
    });

    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Select Date',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              IconButton(
                icon: const Icon(Icons.calendar_today),
                onPressed: () => _selectDate(context),
                tooltip: 'Open calendar',
              ),
            ],
          ),
          const SizedBox(height: AppConstants.smallPadding),
          SizedBox(
            height: 80,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: dates.length,
              itemBuilder: (context, index) {
                final date = dates[index];
                final isSelected = AppDateUtils.isSameDay(date, selectedDate);
                final isToday = AppDateUtils.isSameDay(date, now);

                return GestureDetector(
                  onTap: () => _changeDate(date),
                  child: Container(
                    width: 60,
                    margin: const EdgeInsets.only(right: 8),
                    decoration: BoxDecoration(
                      color: isSelected
                          ? theme.colorScheme.primary
                          : isToday
                          ? theme.colorScheme.primaryContainer
                          : theme.colorScheme.surface,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: isSelected
                            ? theme.colorScheme.primary
                            : theme.colorScheme.outline.withValues(alpha: 0.3),
                      ),
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          AppDateUtils.getDayOfWeek(date),
                          style: theme.textTheme.labelSmall?.copyWith(
                            color: isSelected
                                ? theme.colorScheme.onPrimary
                                : theme.colorScheme.onSurfaceVariant,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          date.day.toString(),
                          style: theme.textTheme.titleMedium?.copyWith(
                            color: isSelected
                                ? theme.colorScheme.onPrimary
                                : isToday
                                ? theme.colorScheme.onPrimaryContainer
                                : theme.colorScheme.onSurface,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        if (isToday && !isSelected)
                          Container(
                            width: 4,
                            height: 4,
                            margin: const EdgeInsets.only(top: 2),
                            decoration: BoxDecoration(
                              color: theme.colorScheme.primary,
                              shape: BoxShape.circle,
                            ),
                          ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickAddSection() {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Quick Add',
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
          ),

          const SizedBox(height: AppConstants.defaultPadding),

          Row(
            children: [
              Expanded(
                child: CustomButton(
                  text: 'Take Photo',
                  onPressed: () => Navigator.pushNamed(
                    context,
                    AppRoutes.camera,
                    arguments: {'mealType': 'snack'},
                  ),
                  icon: Icons.camera_alt,
                ),
              ),

              const SizedBox(width: AppConstants.defaultPadding),

              Expanded(
                child: CustomButton(
                  text: 'Manual Entry',
                  onPressed: () => Navigator.pushNamed(
                    context,
                    AppRoutes.addFood,
                    arguments: {'mealType': 'snack'},
                  ),
                  isOutlined: true,
                  icon: Icons.edit,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildMealsSection(NutritionProvider nutritionProvider) {
    final mealGroups = nutritionProvider.mealGroups;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Today\'s Meals',
          style: Theme.of(
            context,
          ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
        ),

        const SizedBox(height: AppConstants.defaultPadding),

        MealSection(
          title: 'Breakfast',
          icon: Icons.free_breakfast,
          mealType: 'breakfast',
          entries: mealGroups['breakfast'] ?? [],
        ),

        const SizedBox(height: AppConstants.defaultPadding),

        MealSection(
          title: 'Lunch',
          icon: Icons.lunch_dining,
          mealType: 'lunch',
          entries: mealGroups['lunch'] ?? [],
        ),

        const SizedBox(height: AppConstants.defaultPadding),

        MealSection(
          title: 'Dinner',
          icon: Icons.dinner_dining,
          mealType: 'dinner',
          entries: mealGroups['dinner'] ?? [],
        ),

        const SizedBox(height: AppConstants.defaultPadding),

        MealSection(
          title: 'Snacks',
          icon: Icons.cookie,
          mealType: 'snack',
          entries: mealGroups['snack'] ?? [],
        ),
      ],
    );
  }

  Widget _buildRecentEntries(NutritionProvider nutritionProvider) {
    final recentEntries = nutritionProvider.recentEntries;

    if (recentEntries.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Recent Entries',
          style: Theme.of(
            context,
          ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
        ),

        const SizedBox(height: AppConstants.defaultPadding),

        CustomCard(
          child: Column(
            children: recentEntries.take(5).map((entry) {
              return ListTile(
                leading: CircleAvatar(
                  backgroundColor: Theme.of(
                    context,
                  ).colorScheme.primaryContainer,
                  child: Text(
                    entry.name.substring(0, 1).toUpperCase(),
                    style: TextStyle(
                      color: Theme.of(context).colorScheme.onPrimaryContainer,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                title: Text(entry.name),
                subtitle: Text(
                  '${entry.nutritionData.calories.toInt()} cal • ${AppDateUtils.getRelativeDateString(entry.consumedAt)}',
                ),
                trailing: Text(
                  entry.mealType.toUpperCase(),
                  style: Theme.of(context).textTheme.labelSmall?.copyWith(
                    color: Theme.of(context).colorScheme.primary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                onTap: () => Navigator.pushNamed(
                  context,
                  AppRoutes.editFood,
                  arguments: {'foodEntry': entry, 'mealType': entry.mealType},
                ),
              );
            }).toList(),
          ),
        ),
      ],
    );
  }

  Future<void> _selectDate(BuildContext context) async {
    final date = await showDatePicker(
      context: context,
      initialDate: context.read<NutritionProvider>().selectedDate,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now(),
    );

    if (date != null) {
      _changeDate(date);
    }
  }

  void _changeDate(DateTime date) {
    final appProvider = context.read<AppProvider>();
    final nutritionProvider = context.read<NutritionProvider>();

    nutritionProvider.selectDate(date);

    if (appProvider.currentUser != null) {
      nutritionProvider.loadDailyNutrition(
        appProvider.currentUser!.id,
        date: date,
      );
    }
  }

  void _showAddFoodOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Add Food',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),

            const SizedBox(height: AppConstants.defaultPadding),

            ListTile(
              leading: const Icon(Icons.camera_alt),
              title: const Text('Take Photo'),
              subtitle: const Text('Analyze food with AI'),
              onTap: () {
                Navigator.pop(context);
                Navigator.pushNamed(
                  context,
                  AppRoutes.camera,
                  arguments: {'mealType': 'snack'},
                );
              },
            ),

            ListTile(
              leading: const Icon(Icons.edit),
              title: const Text('Manual Entry'),
              subtitle: const Text('Enter nutrition manually'),
              onTap: () {
                Navigator.pop(context);
                Navigator.pushNamed(
                  context,
                  AppRoutes.addFood,
                  arguments: {'mealType': 'snack'},
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}
