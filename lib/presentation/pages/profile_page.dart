import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../../core/constants/app_constants.dart';
import '../../core/routes/app_routes.dart';
import '../../core/widgets/custom_card.dart';
import '../../core/widgets/custom_button.dart';
import '../providers/app_provider.dart';
import '../providers/nutrition_provider.dart';
import '../../domain/entities/user_profile.dart';
import '../../domain/repositories/user_profile_repository.dart';

class ProfilePage extends StatefulWidget {
  const ProfilePage({super.key});

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage>
    with TickerProviderStateMixin {
  bool _isEditing = false;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  // Form controllers
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _nameController;
  late TextEditingController _ageController;
  late TextEditingController _weightController;
  late TextEditingController _heightController;

  String _selectedGender = 'male';
  String _selectedActivityLevel = 'moderately_active';
  String _selectedGoal = 'maintain';
  String _selectedWeightUnit = 'kg';
  String _selectedHeightUnit = 'cm';

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _nameController = TextEditingController();
    _ageController = TextEditingController();
    _weightController = TextEditingController();
    _heightController = TextEditingController();

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _nameController.dispose();
    _ageController.dispose();
    _weightController.dispose();
    _heightController.dispose();
    super.dispose();
  }

  void _populateControllers(UserProfile user) {
    _nameController.text = user.name;
    _ageController.text = user.age.toString();
    _weightController.text = user.weight.toString();
    _heightController.text = user.height.toString();
    _selectedGender = user.gender;
    _selectedActivityLevel = user.activityLevel;
    _selectedGoal = user.goal;
    _selectedWeightUnit = user.weightUnit;
    _selectedHeightUnit = user.heightUnit;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_isEditing ? 'Edit Profile' : 'Profile'),
        actions: [
          if (!_isEditing)
            IconButton(
              icon: const Icon(Icons.edit),
              onPressed: () => _toggleEditMode(context),
            ),
          if (_isEditing) ...[
            IconButton(
              icon: const Icon(Icons.close),
              onPressed: () => _cancelEdit(),
            ),
            IconButton(
              icon: const Icon(Icons.check),
              onPressed: () => _saveProfile(context),
            ),
          ],
        ],
      ),
      body: Consumer<AppProvider>(
        builder: (context, appProvider, child) {
          final user = appProvider.currentUser;

          if (user == null) {
            return _buildNoUserState(context);
          }

          return FadeTransition(
            opacity: _fadeAnimation,
            child: Form(
              key: _formKey,
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(AppConstants.defaultPadding),
                child: Column(
                  children: [
                    // Profile header with enhanced design
                    _buildProfileHeader(context, user),

                    const SizedBox(height: AppConstants.defaultPadding),

                    // Statistics overview
                    if (!_isEditing) _buildStatsOverview(context),

                    const SizedBox(height: AppConstants.defaultPadding),

                    // Basic info section
                    _buildBasicInfoSection(context, user),

                    const SizedBox(height: AppConstants.defaultPadding),

                    // Actions section
                    if (!_isEditing) _buildActionsSection(context),

                    const SizedBox(height: AppConstants.defaultPadding),

                    // Sign out button
                    if (!_isEditing) _buildSignOutButton(context),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildNoUserState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.person_off,
            size: 64,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            'No Profile Found',
            style: Theme.of(
              context,
            ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            'Please complete your profile setup',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          CustomButton(
            text: 'Setup Profile',
            onPressed: () => Navigator.pushNamed(context, AppRoutes.onboarding),
            icon: Icons.person_add,
          ),
        ],
      ),
    );
  }

  Widget _buildProfileHeader(BuildContext context, UserProfile user) {
    return CustomCard(
      child: Column(
        children: [
          Stack(
            children: [
              CircleAvatar(
                radius: 60,
                backgroundColor: Theme.of(context).colorScheme.primary,
                child: Text(
                  user.name.substring(0, 1).toUpperCase(),
                  style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                    color: Theme.of(context).colorScheme.onPrimary,
                    fontWeight: FontWeight.bold,
                    fontSize: 36,
                  ),
                ),
              ),
              if (_isEditing)
                Positioned(
                  bottom: 0,
                  right: 0,
                  child: Container(
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.secondary,
                      shape: BoxShape.circle,
                    ),
                    child: IconButton(
                      icon: Icon(
                        Icons.camera_alt,
                        color: Theme.of(context).colorScheme.onSecondary,
                        size: 20,
                      ),
                      onPressed: () {
                        // TODO: Implement profile picture change
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text(
                              'Profile picture feature coming soon!',
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ),
            ],
          ),

          const SizedBox(height: AppConstants.defaultPadding),

          if (_isEditing)
            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'Name',
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Please enter your name';
                }
                return null;
              },
            )
          else
            Text(
              user.name,
              style: Theme.of(
                context,
              ).textTheme.headlineMedium?.copyWith(fontWeight: FontWeight.bold),
            ),

          const SizedBox(height: AppConstants.smallPadding),

          if (!_isEditing)
            Text(
              '${user.age} years old • ${_formatGender(user.gender)}',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(BuildContext context, String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppConstants.smallPadding),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: Theme.of(context).textTheme.bodyMedium),
          Text(
            value,
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
          ),
        ],
      ),
    );
  }

  String _formatActivityLevel(String activityLevel) {
    switch (activityLevel) {
      case 'sedentary':
        return 'Sedentary';
      case 'lightly_active':
        return 'Lightly Active';
      case 'moderately_active':
        return 'Moderately Active';
      case 'very_active':
        return 'Very Active';
      case 'extremely_active':
        return 'Extremely Active';
      default:
        return activityLevel;
    }
  }

  String _formatGoal(String goal) {
    switch (goal) {
      case 'lose':
        return 'Lose Weight';
      case 'maintain':
        return 'Maintain Weight';
      case 'gain':
        return 'Gain Weight';
      default:
        return goal;
    }
  }

  void _showSignOutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Sign Out'),
        content: const Text('Are you sure you want to sign out?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              context.read<AppProvider>().logout();
              Navigator.pushNamedAndRemoveUntil(context, '/', (route) => false);
            },
            child: Text(
              'Sign Out',
              style: TextStyle(color: Theme.of(context).colorScheme.error),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatsOverview(BuildContext context) {
    return Consumer<NutritionProvider>(
      builder: (context, nutritionProvider, child) {
        final progress = nutritionProvider.progressPercentages;
        final currentData = nutritionProvider.currentDayData;
        final totalNutrition = currentData?.dailyLog.totalNutrition;

        if (currentData == null || totalNutrition == null) {
          return const SizedBox.shrink();
        }

        return CustomCard(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Today\'s Progress',
                style: Theme.of(
                  context,
                ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
              ),
              const SizedBox(height: AppConstants.defaultPadding),
              Row(
                children: [
                  Expanded(
                    child: _buildStatItem(
                      context,
                      'Calories',
                      '${totalNutrition.calories.toInt()}',
                      (progress['calories'] ?? 0.0) / 100,
                      Icons.local_fire_department,
                      Colors.orange,
                    ),
                  ),
                  const SizedBox(width: AppConstants.smallPadding),
                  Expanded(
                    child: _buildStatItem(
                      context,
                      'Protein',
                      '${totalNutrition.protein.toInt()}g',
                      (progress['protein'] ?? 0.0) / 100,
                      Icons.fitness_center,
                      Colors.red,
                    ),
                  ),
                  const SizedBox(width: AppConstants.smallPadding),
                  Expanded(
                    child: _buildStatItem(
                      context,
                      'Carbs',
                      '${totalNutrition.carbs.toInt()}g',
                      (progress['carbs'] ?? 0.0) / 100,
                      Icons.grain,
                      Colors.amber,
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildStatItem(
    BuildContext context,
    String label,
    String value,
    double progress,
    IconData icon,
    Color color,
  ) {
    return Column(
      children: [
        Icon(icon, color: color, size: 24),
        const SizedBox(height: 4),
        Text(
          value,
          style: Theme.of(
            context,
          ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.bold),
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.labelSmall?.copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
        const SizedBox(height: 4),
        LinearProgressIndicator(
          value: progress.clamp(0.0, 1.0),
          backgroundColor: color.withValues(alpha: 0.2),
          valueColor: AlwaysStoppedAnimation<Color>(color),
        ),
      ],
    );
  }

  Widget _buildBasicInfoSection(BuildContext context, UserProfile user) {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Basic Information',
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: AppConstants.defaultPadding),

          if (_isEditing) ...[
            // Editing form fields
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _ageController,
                    decoration: const InputDecoration(
                      labelText: 'Age',
                      border: OutlineInputBorder(),
                      suffixText: 'years',
                    ),
                    keyboardType: TextInputType.number,
                    inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Required';
                      }
                      final age = int.tryParse(value);
                      if (age == null || age < 13 || age > 120) {
                        return 'Invalid age';
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: AppConstants.defaultPadding),
                Expanded(
                  child: DropdownButtonFormField<String>(
                    value: _selectedGender,
                    decoration: const InputDecoration(
                      labelText: 'Gender',
                      border: OutlineInputBorder(),
                    ),
                    items: const [
                      DropdownMenuItem(value: 'male', child: Text('Male')),
                      DropdownMenuItem(value: 'female', child: Text('Female')),
                    ],
                    onChanged: (value) {
                      if (value != null) {
                        setState(() => _selectedGender = value);
                      }
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.defaultPadding),

            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _weightController,
                    decoration: InputDecoration(
                      labelText: 'Weight',
                      border: const OutlineInputBorder(),
                      suffixText: _selectedWeightUnit,
                    ),
                    keyboardType: const TextInputType.numberWithOptions(
                      decimal: true,
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Required';
                      }
                      final weight = double.tryParse(value);
                      if (weight == null || weight <= 0) {
                        return 'Invalid weight';
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: AppConstants.defaultPadding),
                Expanded(
                  child: TextFormField(
                    controller: _heightController,
                    decoration: InputDecoration(
                      labelText: 'Height',
                      border: const OutlineInputBorder(),
                      suffixText: _selectedHeightUnit,
                    ),
                    keyboardType: const TextInputType.numberWithOptions(
                      decimal: true,
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Required';
                      }
                      final height = double.tryParse(value);
                      if (height == null || height <= 0) {
                        return 'Invalid height';
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.defaultPadding),

            DropdownButtonFormField<String>(
              value: _selectedActivityLevel,
              decoration: const InputDecoration(
                labelText: 'Activity Level',
                border: OutlineInputBorder(),
              ),
              items: const [
                DropdownMenuItem(value: 'sedentary', child: Text('Sedentary')),
                DropdownMenuItem(
                  value: 'lightly_active',
                  child: Text('Lightly Active'),
                ),
                DropdownMenuItem(
                  value: 'moderately_active',
                  child: Text('Moderately Active'),
                ),
                DropdownMenuItem(
                  value: 'very_active',
                  child: Text('Very Active'),
                ),
                DropdownMenuItem(
                  value: 'extremely_active',
                  child: Text('Extremely Active'),
                ),
              ],
              onChanged: (value) {
                if (value != null) {
                  setState(() => _selectedActivityLevel = value);
                }
              },
            ),
            const SizedBox(height: AppConstants.defaultPadding),

            DropdownButtonFormField<String>(
              value: _selectedGoal,
              decoration: const InputDecoration(
                labelText: 'Goal',
                border: OutlineInputBorder(),
              ),
              items: const [
                DropdownMenuItem(value: 'lose', child: Text('Lose Weight')),
                DropdownMenuItem(
                  value: 'maintain',
                  child: Text('Maintain Weight'),
                ),
                DropdownMenuItem(value: 'gain', child: Text('Gain Weight')),
              ],
              onChanged: (value) {
                if (value != null) {
                  setState(() => _selectedGoal = value);
                }
              },
            ),
          ] else ...[
            // Display mode
            _buildInfoRow(context, 'Age', '${user.age} years old'),
            _buildInfoRow(context, 'Gender', _formatGender(user.gender)),
            _buildInfoRow(
              context,
              'Weight',
              '${user.weight} ${user.weightUnit}',
            ),
            _buildInfoRow(
              context,
              'Height',
              '${user.height} ${user.heightUnit}',
            ),
            _buildInfoRow(
              context,
              'Activity Level',
              _formatActivityLevel(user.activityLevel),
            ),
            _buildInfoRow(context, 'Goal', _formatGoal(user.goal)),
          ],
        ],
      ),
    );
  }

  Widget _buildActionsSection(BuildContext context) {
    return CustomCard(
      child: Column(
        children: [
          ListTile(
            leading: const Icon(Icons.track_changes),
            title: const Text('Nutrition Goals'),
            subtitle: const Text('View and edit your daily targets'),
            trailing: const Icon(Icons.chevron_right),
            onTap: () => Navigator.pushNamed(context, AppRoutes.goals),
          ),
          const Divider(),
          ListTile(
            leading: const Icon(Icons.analytics),
            title: const Text('Analytics'),
            subtitle: const Text('View your progress and insights'),
            trailing: const Icon(Icons.chevron_right),
            onTap: () => Navigator.pushNamed(context, AppRoutes.analytics),
          ),
          const Divider(),
          ListTile(
            leading: const Icon(Icons.settings),
            title: const Text('Settings'),
            subtitle: const Text('App preferences and privacy'),
            trailing: const Icon(Icons.chevron_right),
            onTap: () => Navigator.pushNamed(context, AppRoutes.settings),
          ),
        ],
      ),
    );
  }

  Widget _buildSignOutButton(BuildContext context) {
    return CustomButton(
      text: 'Sign Out',
      onPressed: () => _showSignOutDialog(context),
      isOutlined: true,
      icon: Icons.logout,
      textColor: Theme.of(context).colorScheme.error,
      backgroundColor: Theme.of(context).colorScheme.error,
      width: double.infinity,
    );
  }

  void _toggleEditMode(BuildContext context) {
    final user = context.read<AppProvider>().currentUser;
    if (user != null) {
      _populateControllers(user);
      setState(() => _isEditing = true);
    }
  }

  void _cancelEdit() {
    setState(() => _isEditing = false);
  }

  Future<void> _saveProfile(BuildContext context) async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    try {
      final appProvider = context.read<AppProvider>();
      final currentUser = appProvider.currentUser;
      if (currentUser == null) return;

      final updatedUser = currentUser.copyWith(
        name: _nameController.text.trim(),
        age: int.parse(_ageController.text),
        gender: _selectedGender,
        weight: double.parse(_weightController.text),
        height: double.parse(_heightController.text),
        activityLevel: _selectedActivityLevel,
        goal: _selectedGoal,
        weightUnit: _selectedWeightUnit,
        heightUnit: _selectedHeightUnit,
        updatedAt: DateTime.now(),
      );

      // Update in repository
      final repository = context.read<UserProfileRepository>();
      final result = await repository.updateUserProfile(updatedUser);

      if (result.isSuccess) {
        await appProvider.updateUserProfile(updatedUser);
        setState(() => _isEditing = false);

        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Profile updated successfully!'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Failed to update profile: ${result.failureValue?.message}',
              ),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error updating profile: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  String _formatGender(String gender) {
    return gender == 'male' ? 'Male' : 'Female';
  }
}
