import 'package:intl/intl.dart';
import '../constants/app_constants.dart';

class AppDateUtils {
  static final DateFormat _dateFormat = DateFormat(AppConstants.dateFormat);
  static final DateFormat _timeFormat = DateFormat(AppConstants.timeFormat);
  static final DateFormat _dateTimeFormat = DateFormat(
    AppConstants.dateTimeFormat,
  );
  static final DateFormat _displayDateFormat = DateFormat(
    AppConstants.displayDateFormat,
  );

  /// Get current date as string in yyyy-MM-dd format
  static String getCurrentDateString() {
    return _dateFormat.format(DateTime.now());
  }

  /// Get current time as string in HH:mm format
  static String getCurrentTimeString() {
    return _timeFormat.format(DateTime.now());
  }

  /// Get current date and time as string in yyyy-MM-dd HH:mm:ss format
  static String getCurrentDateTimeString() {
    return _dateTimeFormat.format(DateTime.now());
  }

  /// Format date for display (e.g., "Jan 15, 2024")
  static String formatDateForDisplay(DateTime date) {
    return _displayDateFormat.format(date);
  }

  /// Format date as string in yyyy-MM-dd format
  static String formatDateAsString(DateTime date) {
    return _dateFormat.format(date);
  }

  /// Parse date string in yyyy-MM-dd format to DateTime
  static DateTime? parseDateString(String dateString) {
    try {
      return _dateFormat.parse(dateString);
    } catch (e) {
      return null;
    }
  }

  /// Parse date time string in yyyy-MM-dd HH:mm:ss format to DateTime
  static DateTime? parseDateTimeString(String dateTimeString) {
    try {
      return _dateTimeFormat.parse(dateTimeString);
    } catch (e) {
      return null;
    }
  }

  /// Check if two dates are the same day
  static bool isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }

  /// Check if date is today
  static bool isToday(DateTime date) {
    return isSameDay(date, DateTime.now());
  }

  /// Check if date is yesterday
  static bool isYesterday(DateTime date) {
    final yesterday = DateTime.now().subtract(const Duration(days: 1));
    return isSameDay(date, yesterday);
  }

  /// Get start of day (00:00:00)
  static DateTime getStartOfDay(DateTime date) {
    return DateTime(date.year, date.month, date.day);
  }

  /// Get end of day (23:59:59)
  static DateTime getEndOfDay(DateTime date) {
    return DateTime(date.year, date.month, date.day, 23, 59, 59);
  }

  /// Get start of week (Monday)
  static DateTime getStartOfWeek(DateTime date) {
    final daysFromMonday = date.weekday - 1;
    return getStartOfDay(date.subtract(Duration(days: daysFromMonday)));
  }

  /// Get end of week (Sunday)
  static DateTime getEndOfWeek(DateTime date) {
    final daysToSunday = 7 - date.weekday;
    return getEndOfDay(date.add(Duration(days: daysToSunday)));
  }

  /// Get start of month
  static DateTime getStartOfMonth(DateTime date) {
    return DateTime(date.year, date.month, 1);
  }

  /// Get end of month
  static DateTime getEndOfMonth(DateTime date) {
    final nextMonth = date.month == 12
        ? DateTime(date.year + 1, 1, 1)
        : DateTime(date.year, date.month + 1, 1);
    return nextMonth.subtract(const Duration(days: 1));
  }

  /// Get days between two dates
  static int getDaysBetween(DateTime start, DateTime end) {
    final startDate = getStartOfDay(start);
    final endDate = getStartOfDay(end);
    return endDate.difference(startDate).inDays;
  }

  /// Get list of dates in a range
  static List<DateTime> getDateRange(DateTime start, DateTime end) {
    final dates = <DateTime>[];
    var current = getStartOfDay(start);
    final endDate = getStartOfDay(end);

    while (current.isBefore(endDate) || current.isAtSameMomentAs(endDate)) {
      dates.add(current);
      current = current.add(const Duration(days: 1));
    }

    return dates;
  }

  /// Get relative date string (e.g., "Today", "Yesterday", "2 days ago")
  static String getRelativeDateString(DateTime date) {
    final now = DateTime.now();
    final difference = getDaysBetween(date, now);

    if (difference == 0) {
      return 'Today';
    } else if (difference == 1) {
      return 'Yesterday';
    } else if (difference < 7) {
      return '$difference days ago';
    } else if (difference < 30) {
      final weeks = (difference / 7).floor();
      return weeks == 1 ? '1 week ago' : '$weeks weeks ago';
    } else {
      return formatDateForDisplay(date);
    }
  }

  /// Get week dates (Monday to Sunday)
  static List<DateTime> getWeekDates(DateTime date) {
    final startOfWeek = getStartOfWeek(date);
    return List.generate(7, (index) => startOfWeek.add(Duration(days: index)));
  }

  /// Get month dates
  static List<DateTime> getMonthDates(DateTime date) {
    final startOfMonth = getStartOfMonth(date);
    final endOfMonth = getEndOfMonth(date);
    return getDateRange(startOfMonth, endOfMonth);
  }

  /// Get day of week as short string (e.g., "Mon", "Tue", etc.)
  static String getDayOfWeek(DateTime date) {
    const days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
    return days[date.weekday - 1];
  }
}
